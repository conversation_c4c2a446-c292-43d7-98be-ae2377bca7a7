import pandas as pd
import os

def merge_csv(input_paths, output_path):
    # 创建一个空的 DataFrame 用来存储合并后的数据
    merged_df = pd.DataFrame()

    # 遍历每一个输入路径，将每个 CSV 文件读入并追加到合并的 DataFrame 中
    for file_path in input_paths:
        # 检查文件是否存在
        if os.path.exists(file_path):
            # 读取 CSV 文件
            df = pd.read_csv(file_path)
            # 将当前的 DataFrame 拼接到合并的 DataFrame 上
            merged_df = pd.concat([merged_df, df], ignore_index=True)
        else:
            print(f"文件 {file_path} 不存在！")

    # 将合并后的数据写入到输出的 CSV 文件
    merged_df.to_csv(output_path, index=False)
    print(f"所有 CSV 文件已成功合并，输出文件路径：{output_path}")

# 示例用法：
if __name__ == "__main__":
    # 输入 CSV 文件的路径列表
    input_paths = [
        '/Users/<USER>/Documents/bscan_merged_result_234857_sheet1.csv',
        '/Users/<USER>/Documents/bscan_merged_result_024538_sheet1.csv',
        '/Users/<USER>/Documents/bscan_merged_result_024545_sheet1.csv'
    ]
    
    # 输出合并后的 CSV 文件路径
    output_path = '/Users/<USER>/Documents/combine_sheet1.csv'

    # 调用函数合并 CSV 文件
    merge_csv(input_paths, output_path)
