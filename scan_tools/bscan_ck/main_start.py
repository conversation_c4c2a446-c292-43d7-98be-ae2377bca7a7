#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from typing import Set
import re
from datetime import datetime

from .config import settings
from .core import scanner, utils
from .database.db_handler import <PERSON>Handler

# from scan_tools.scan_model.db_handler import DatabaseHandler

def run_initial_scan():
    """
    任务一: 执行原始扫描，并将结果写入数据库。
    (支持分片, 保留分片日志)
    """
    print("\n" + "="*20 + " 任务一: 开始执行ClickHouse原始扫描 " + "="*20)
    
    # 1. 准备工作
    raw_targets_path = os.path.abspath(settings.RAW_TARGETS_FILE)
    if not os.path.exists(raw_targets_path):
        print(f"致命错误: 原始扫描目标文件未找到: {raw_targets_path}")
        return

    bscan_executable_path = os.path.abspath(os.path.join(settings.BSCAN_DIR, settings.BSCAN_EXECUTABLE))
    poc_file_path = os.path.abspath(settings.BSCAN_POC_FILE)

    # 2. 对大文件进行分片
    print("信息: 正在将大型IP列表文件分片...")
    chunk_files = utils.split_file_into_chunks(
        filepath=raw_targets_path,
        chunk_size=settings.IP_CHUNK_SIZE,
        temp_dir=settings.TEMP_CHUNK_DIR
    )
    if not chunk_files:
        print("错误: IP文件分片失败，中止原始扫描。")
        return

    # 3. 创建带时间戳的、用于存放分片结果的临时目录
    run_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    chunk_log_dir_for_run = os.path.join(settings.CHUNK_LOG_BASE_DIR, f"run_{run_timestamp}")
    os.makedirs(chunk_log_dir_for_run, exist_ok=True)
    print(f"信息: 本次运行的分片结果将保存在: {chunk_log_dir_for_run}")
    
    # 4. 遍历分片进行扫描
    total_chunks = len(chunk_files)
    for i, chunk_file in enumerate(chunk_files):
        print(f"\n--- 正在处理分片 {i + 1}/{total_chunks}: {os.path.basename(chunk_file)} ---")
        
        # bscan 会在 cwd (当前工作目录) 生成报告, 所以我们将结果直接输出到带时间戳的目录
        scan_success = scanner.run_bscan(
            target_file_path=chunk_file,
            ports=settings.BSCAN_PORTS,
            poc_file=poc_file_path,
            bscan_executable=bscan_executable_path,
            output_dir=chunk_log_dir_for_run # 将所有分片结果都输出到同一个带时间戳的目录
        )

        if not scan_success:
            print(f"警告: 分片 {os.path.basename(chunk_file)} 扫描失败，跳过此分片。")
    
    # 5. 合并所有分片的XLSX结果 (仅合并Sheet1用于概览)
    # 注意: bscan的输出文件名是动态的，我们需要查找该目录下所有的xlsx文件
    chunk_xlsx_files = [os.path.join(chunk_log_dir_for_run, f) for f in os.listdir(chunk_log_dir_for_run) if f.lower().endswith('.xlsx')]

    if not chunk_xlsx_files:
        print("错误: 所有分片扫描均未产生有效的结果报告，任务中止。")
        utils.cleanup_temp_chunks(settings.TEMP_CHUNK_DIR)
        # 即使失败，也保留空的运行日志目录以供排查
        return
        
    final_report_name = f"bscan_merged_result_{run_timestamp}.xlsx"
    final_report_path = os.path.join(settings.BSCAN_ORIGIN_LOG_DIR, final_report_name)
    
    utils.merge_xlsx_files(chunk_xlsx_files, final_report_path)

    # 6. 处理合并后的最终报告以及分片日志目录
    if os.path.exists(final_report_path):
        print("\n信息: 开始处理合并后的最终扫描报告和分片日志...")
        # 传入合并报告的路径和分片日志的目录
        process_latest_initial_report(
            specific_report_path=final_report_path,
            chunk_log_dir=chunk_log_dir_for_run
        )
    else:
        print("\n错误: 最终合并报告未生成，跳过数据库入库步骤。")

    # 7. 清理临时分片文件，但保留分片结果日志
    utils.cleanup_temp_chunks(settings.TEMP_CHUNK_DIR)
    
    print("\n" + "="*20 + " 任务一: ClickHouse原始扫描完成 " + "="*20)

def run_audit_scan():
    """
    任务二: 执行复测扫描，并更新漏洞状态。
    """
    print("\n" + "="*20 + " 任务二: 开始执行ClickHouse复测扫描 " + "="*20)
    
    # 1. 准备复测目标
    db_handler = DatabaseHandler(settings.DB_CONFIG)
    targets_to_retest = db_handler.fetch_active_vulnerabilities_for_audit(
        risk_type=settings.RISK_TYPE_CLICKHOUSE
    )
    if not targets_to_retest:
        print("信息: 数据库中没有需要复测的ClickHouse活跃漏洞，任务结束。")
        return

    audit_targets_path = os.path.abspath(settings.AUDIT_TARGETS_FILE)
    with open(audit_targets_path, 'w') as f:
        for item in targets_to_retest:
            f.write(f"{item['host']}\n")
    print(f"信息: 已将 {len(targets_to_retest)} 个目标写入复测文件: {audit_targets_path}")
    
    # 2. 执行bscan进行复测
    bscan_executable_path = os.path.abspath(os.path.join(settings.BSCAN_DIR, settings.BSCAN_EXECUTABLE))
    poc_file_path = os.path.abspath(settings.BSCAN_POC_FILE)
    
    scan_success = scanner.run_bscan(
        target_file_path=audit_targets_path,
        ports=settings.BSCAN_PORTS,
        poc_file=poc_file_path,
        bscan_executable=bscan_executable_path,
        output_dir=settings.BSCAN_AUDIT_LOG_DIR
    )
    
    os.remove(audit_targets_path)
    print(f"信息: 已清理临时复测文件: {audit_targets_path}")

    if not scan_success:
        print("警告: 复测扫描bscan执行失败，将中止本次复测流程以防数据错误。")
        return

    # 3. 处理复测结果
    process_latest_audit_report()
    
    print("\n" + "="*20 + " 任务二: ClickHouse复测扫描完成 " + "="*20)

def process_latest_initial_report(specific_report_path: str = None, chunk_log_dir: str = None):
    """
    功能七: 处理指定的或最新的原始扫描报告，并添加新漏洞。
    如果提供了 chunk_log_dir，将从该目录解析所有分片的Sheet2。
    如果未提供，则会尝试根据报告文件名自动定位对应的chunk_log_dir。
    """
    print("\n" + "="*20 + " 功能七: 处理ClickHouse原始报告 " + "="*20)
    
    report_to_process = specific_report_path
    if not report_to_process:
        print("信息: 未指定特定报告，将自动查找最新报告进行处理...")
        report_to_process = scanner.find_latest_bscan_report(settings.BSCAN_ORIGIN_LOG_DIR)
    
    if not report_to_process or not os.path.exists(report_to_process):
        print("错误: 在原始扫描目录中未找到任何可处理的报告。")
        return

    # 智能判断chunk_log_dir
    # 如果 chunk_log_dir 没有被直接提供 (例如，当作为独立功能7运行时)
    if not chunk_log_dir:
        print("信息: 未直接提供分片日志目录，尝试根据报告文件名自动定位...")
        report_filename = os.path.basename(report_to_process)
        # 从 "bscan_merged_result_YYYYMMDD_HHMMSS.xlsx" 中提取时间戳
        match = re.search(r'(\d{8}_\d{6})', report_filename)
        if match:
            timestamp = match.group(1)
            potential_chunk_dir = os.path.join(settings.CHUNK_LOG_BASE_DIR, f"run_{timestamp}")
            if os.path.isdir(potential_chunk_dir):
                chunk_log_dir = potential_chunk_dir
                print(f"成功: 已自动定位到对应的分片日志目录: {os.path.basename(chunk_log_dir)}")
            else:
                print(f"警告: 根据时间戳未能找到对应的分片目录: {potential_chunk_dir}")
        else:
            print("警告: 无法从报告文件名中解析出时间戳，将按单个文件处理。")

    db_handler = DatabaseHandler(settings.DB_CONFIG)
    ip_owner_info = utils.load_ip_info(settings.IP_INFO_CSV_PATH)

    # 传入报告路径和(可能已自动定位的)chunk_log_dir
    scanner.process_bscan_report_to_db(report_to_process, db_handler, ip_owner_info, chunk_log_dir=chunk_log_dir)
    print("\n" + "="*20 + " 功能七: 处理完成 " + "="*20)

def process_latest_audit_report(specific_report_path: str = None):
    """
    功能八: 处理指定的或最新的复测扫描报告，并更新漏洞状态。
    """
    print("\n" + "="*20 + " 功能八: 处理ClickHouse复测报告 " + "="*20)
    
    latest_report = specific_report_path
    if not latest_report:
        print("信息: 未指定特定报告，将自动查找最新报告进行处理...")
        latest_report = scanner.find_latest_bscan_report(settings.BSCAN_AUDIT_LOG_DIR)

    if not latest_report or not os.path.exists(latest_report):
        print("错误: 在复测扫描目录中未找到任何可处理的报告。")
        return

    db_handler = DatabaseHandler(settings.DB_CONFIG)
    scanner.process_audit_report_and_update_db(latest_report, db_handler)
    print("\n" + "="*20 + " 功能八: 处理完成 " + "="*20)
