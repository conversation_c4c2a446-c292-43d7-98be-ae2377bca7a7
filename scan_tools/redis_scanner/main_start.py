#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from typing import Set, Optional
from datetime import datetime
import re

from .config import settings
from .core import scanner, utils
from .database.models import Vulnerability

from scan_tools.scan_model.db_handler import DatabaseHandler

# --- 核心扫描与处理逻辑 ---

def run_initial_scan():
    """
    任务一: 执行原始扫描，并将结果写入数据库。
    (支持分片, 保留分片日志)
    """
    print("\n" + "="*20 + " 任务一: 开始执行Redis未授权访问原始扫描 " + "="*20)
    
    # 1. 准备工作
    if not os.path.exists(settings.RAW_TARGETS_FILE):
        print(f"致命错误: 原始扫描目标文件未找到: {settings.RAW_TARGETS_FILE}")
        return

    # 2. 对大文件进行分片
    print("信息: 正在将大型IP列表文件分片...")
    chunk_files = utils.split_file_into_chunks(
        filepath=settings.RAW_TARGETS_FILE,
        chunk_size=settings.IP_CHUNK_SIZE,
        temp_dir=settings.TEMP_CHUNK_DIR
    )
    if not chunk_files:
        print("错误: IP文件分片失败，中止原始扫描。")
        return

    # 3. 创建一个本次运行统一的、带时间戳的总日志文件
    run_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    total_log_filename = f"fscan_initial_run_{run_timestamp}.log"
    total_log_path = os.path.join(settings.LOG_DIR, total_log_filename)
    print(f"信息: 本次所有分片扫描结果将统一追加到: {total_log_path}")

    # 4. 遍历分片进行扫描
    total_chunks = len(chunk_files)
    for i, chunk_file in enumerate(chunk_files):
        print(f"\n--- 正在处理分片 {i + 1}/{total_chunks}: {os.path.basename(chunk_file)} ---")
        
        scanner.run_fscan(
            target_file_path=chunk_file,
            ports=settings.FSCAN_PORTS,
            log_file_path=total_log_path,
            fscan_executable=settings.FSCAN_EXECUTABLE,
            threads=settings.FSCAN_THREADS
        )
    
    utils.cleanup_temp_chunks(settings.TEMP_CHUNK_DIR)
    
    # 5. 直接处理刚刚生成的总日志文件
    if os.path.exists(total_log_path):
        print("\n信息: 所有分片扫描完成，开始处理最终的日志文件...")
        process_latest_initial_report(specific_log_path=total_log_path)
    else:
        print("\n警告: 扫描未产生任何结果日志，任务结束。")

    print("\n" + "="*20 + " 任务一: Redis未授权访问原始扫描完成 " + "="*20)


def run_audit_scan():
    """
    任务二: 执行复测扫描，并更新漏洞状态。
    """
    print("\n" + "="*20 + " 任务二: 开始执行Redis未授权访问复测扫描 " + "="*20)
    db_handler = DatabaseHandler(settings.DB_CONFIG)

    # 1. 拉取待复测目标
    targets_to_retest = db_handler.fetch_active_vulnerabilities_for_audit(
        risk_type=settings.RISK_TYPE_REDIS
    )
    if not targets_to_retest:
        print("信息: 数据库中没有需要复测的Redis活跃漏洞，任务结束。")
        return

    # 2. 将目标写入临时文件
    with open(settings.AUDIT_TARGETS_FILE, 'w') as f:
        for item in targets_to_retest:
            f.write(f"{item['host']}\n")
    print(f"信息: 已将 {len(targets_to_retest)} 个目标写入复测文件: {settings.AUDIT_TARGETS_FILE}")
    
    # 3. 创建复测日志文件并执行扫描
    run_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    audit_log_filename = f"fscan_audit_run_{run_timestamp}.log"
    audit_log_path = os.path.join(settings.LOG_DIR, audit_log_filename)
    
    scan_success = scanner.run_fscan(
        target_file_path=settings.AUDIT_TARGETS_FILE,
        ports=settings.FSCAN_PORTS,
        log_file_path=audit_log_path,
        fscan_executable=settings.FSCAN_EXECUTABLE,
        threads=settings.FSCAN_THREADS
    )

    os.remove(settings.AUDIT_TARGETS_FILE)
    print("信息: 已清理临时复测目标文件。")

    if not scan_success:
        print("警告: 复测扫描执行失败，中止本次流程。")
        return
        
    # 4. 处理复测结果日志
    if os.path.exists(audit_log_path):
        process_latest_audit_report(specific_log_path=audit_log_path)
    else:
        print("警告: 复测扫描未产生日志文件，无法进行状态更新。")

    print("\n" + "="*20 + " 任务二: Redis未授权访问复测扫描完成 " + "="*20)


def process_latest_initial_report(specific_log_path: Optional[str] = None):
    """
    功能三: 处理指定的或最新的原始扫描报告，并添加新漏洞。
    """
    print("\n" + "="*20 + " 功能三: 处理Redis原始报告 " + "="*20)
    
    log_to_process = specific_log_path
    if not log_to_process:
        print("信息: 未指定特定日志，将自动查找最新日志进行处理...")
        log_to_process = find_latest_log(settings.LOG_DIR, "initial")
    
    if not log_to_process or not os.path.exists(log_to_process):
        print("错误: 在日志目录中未找到任何可处理的原始扫描日志。")
        return

    db_handler = DatabaseHandler(settings.DB_CONFIG)
    ip_owner_info = utils.load_ip_owner_info(settings.IP_INFO_CSV_PATH)
    live_vulns_set = scanner.parse_fscan_log(log_to_process)

    if not live_vulns_set:
        print(f"信息: 日志 {os.path.basename(log_to_process)} 中未发现任何Redis漏洞。")
        return

    print(f"信息: 开始将 {len(live_vulns_set)} 个新发现的漏洞写入数据库...")
    for host_port in live_vulns_set:
        try:
            host, port_str = host_port.split(':')
            port = int(port_str)
            vuln = Vulnerability(host=host, port=port, url=f"http://{host_port}")
            owner = ip_owner_info.get(host, {})
            db_handler.add_vulnerability(vuln, owner)
        except Exception as e:
            print(f"错误: 处理漏洞 {host_port} 并存入数据库时失败: {e}")
            
    print("\n" + "="*20 + " 功能三: 处理完成 " + "="*20)


def process_latest_audit_report(specific_log_path: Optional[str] = None):
    """
    功能四: 处理指定的或最新的复测扫描报告，并更新漏洞状态。
    """
    print("\n" + "="*20 + " 功能四: 处理Redis复测报告 " + "="*20)
    
    log_to_process = specific_log_path
    if not log_to_process:
        print("信息: 未指定特定日志，将自动查找最新日志进行处理...")
        log_to_process = find_latest_log(settings.LOG_DIR, "audit")
    
    if not log_to_process or not os.path.exists(log_to_process):
        print("错误: 在日志目录中未找到任何可处理的复测日志。")
        return

    db_handler = DatabaseHandler(settings.DB_CONFIG)
    
    # 1. 获取所有待复测的目标，用于比对
    targets_to_retest = db_handler.fetch_active_vulnerabilities_for_audit(risk_type=settings.RISK_TYPE_REDIS)
    initial_targets_set = {f"{item['host']}:{item['port']}" for item in targets_to_retest}

    # 2. 从复测日志中解析出仍然存活的漏洞
    live_vulns_in_audit = scanner.parse_fscan_log(log_to_process)
    print(f"信息: 复测扫描发现 {len(live_vulns_in_audit)} 个仍然存活的漏洞。")

    # 3. 计算出已修复的漏洞（在待复测列表但不在存活列表）
    fixed_vulns_set = initial_targets_set - live_vulns_in_audit
    print(f"信息: 计算得出 {len(fixed_vulns_set)} 个漏洞已被修复。")
    
    # 4. 更新数据库
    if fixed_vulns_set:
        db_handler.batch_update_status_to_fixed(
            fixed_host_ports=list(fixed_vulns_set),
            risk_type=settings.RISK_TYPE_REDIS
        )
    print("\n" + "="*20 + " 功能四: 处理完成 " + "="*20)

# --- 辅助函数 ---

def find_latest_log(log_dir: str, log_type: str) -> Optional[str]:
    """
    在指定目录中查找最新的日志文件。
    log_type: "initial" 或 "audit"
    """
    prefix = f"fscan_{log_type}_run_"
    if not os.path.isdir(log_dir):
        return None
    files = [os.path.join(log_dir, f) for f in os.listdir(log_dir) if f.startswith(prefix) and f.endswith('.log')]
    if not files:
        return None
    return max(files, key=os.path.getctime)
